"""
Car Index Validator - Ensures consistency between profile.json and garage.json car indices
"""
import json
import os

class CarIndexValidator:
    @staticmethod
    def validate_and_fix_car_indices():
        """
        Validate and fix car index consistency between profile.json and garage.json
        Returns: (success, message, changes_made)
        """
        try:
            # Load profile data
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            # Load garage data
            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)
            
            changes_made = []
            
            # Get owned cars and selected car
            owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
            selected_car_index = profile_data.get("cars", {}).get("selected_car", -1)
            
            # Validate basic structure
            if not owned_cars:
                return True, "No cars owned - validation not needed", []
            
            if not garage_data:
                return False, "Garage data is empty but cars are owned", []
            
            # Check if selected car index is valid for owned cars array
            if selected_car_index < 0 or selected_car_index >= len(owned_cars):
                # Fix: Set to first car if available
                if owned_cars:
                    old_index = selected_car_index
                    profile_data["cars"]["selected_car"] = 0
                    changes_made.append(f"Fixed selected_car index from {old_index} to 0")
                    selected_car_index = 0
                else:
                    return False, "No valid cars to select", []
            
            # Check if garage data has corresponding entry
            if selected_car_index >= len(garage_data):
                return False, f"Selected car index {selected_car_index} exceeds garage data length {len(garage_data)}", []
            
            # Validate that garage entry index matches array position
            garage_entry = garage_data[selected_car_index]
            if garage_entry.get("index") != selected_car_index:
                old_index = garage_entry.get("index")
                garage_entry["index"] = selected_car_index
                changes_made.append(f"Fixed garage entry index from {old_index} to {selected_car_index}")
            
            # Validate that car names match
            selected_car_name = owned_cars[selected_car_index]
            garage_car_name = garage_entry.get("name")
            if selected_car_name != garage_car_name:
                changes_made.append(f"Warning: Car name mismatch - owned: '{selected_car_name}', garage: '{garage_car_name}'")
            
            # Clean up system data for non-existent cars
            system_data_keys = ["usage_data", "fuel_data", "tire_data", "maintenance_data"]
            for system_key in system_data_keys:
                if system_key in profile_data:
                    cars_data = profile_data[system_key].get("cars", {})
                    valid_indices = set(str(i) for i in range(len(owned_cars)))
                    existing_indices = set(cars_data.keys())
                    
                    # Remove data for non-existent cars
                    invalid_indices = existing_indices - valid_indices
                    for invalid_index in invalid_indices:
                        del cars_data[invalid_index]
                        changes_made.append(f"Removed {system_key} for invalid car index {invalid_index}")
                    
                    # Ensure data exists for all valid cars
                    for valid_index in valid_indices:
                        if valid_index not in cars_data:
                            # Create default data based on system type
                            if system_key == "usage_data":
                                cars_data[valid_index] = {
                                    "car_age_days": 0,
                                    "races_completed": 0,
                                    "engine_age_days": 0,
                                    "turbo_age_days": 0,
                                    "intercooler_age_days": 0,
                                    "ecu_age_days": 0,
                                    "last_update": int(time.time()) if 'time' in globals() else 0
                                }
                            elif system_key == "fuel_data":
                                cars_data[valid_index] = {
                                    "current_fuel": 60,
                                    "max_capacity": 60,
                                    "last_refuel": int(time.time()) if 'time' in globals() else 0
                                }
                            elif system_key == "tire_data":
                                cars_data[valid_index] = {
                                    "tire_type": "standard",
                                    "condition": 100.0,
                                    "total_distance": 0.0,
                                    "last_replacement": int(time.time()) if 'time' in globals() else 0
                                }
                            elif system_key == "maintenance_data":
                                cars_data[valid_index] = {
                                    "last_maintenance": int(time.time()) if 'time' in globals() else 0,
                                    "last_maintenance_races": 0,
                                    "maintenance_due": False,
                                    "total_maintenance_cost": 0,
                                    "crashes": 0,
                                    "repair_history": []
                                }
                            changes_made.append(f"Created missing {system_key} for car index {valid_index}")
            
            # Save changes if any were made
            if changes_made:
                with open('data/profile.json', 'w') as f:
                    json.dump(profile_data, f, indent=4)
                
                with open('data/garage.json', 'w') as f:
                    json.dump(garage_data, f, indent=4)
            
            return True, "Car indices validated successfully", changes_made
            
        except FileNotFoundError as e:
            return False, f"Required file not found: {e}", []
        except json.JSONDecodeError as e:
            return False, f"JSON decode error: {e}", []
        except Exception as e:
            return False, f"Validation error: {e}", []
    
    @staticmethod
    def quick_validate():
        """
        Quick validation check without making changes
        Returns: (is_valid, issues)
        """
        try:
            with open('data/profile.json', 'r') as f:
                profile_data = json.load(f)
            
            with open('data/garage.json', 'r') as f:
                garage_data = json.load(f)
            
            issues = []
            
            owned_cars = profile_data.get("inventory", {}).get("owned_cars", [])
            selected_car_index = profile_data.get("cars", {}).get("selected_car", -1)
            
            if not owned_cars:
                return True, []  # No cars = no issues
            
            if selected_car_index < 0 or selected_car_index >= len(owned_cars):
                issues.append(f"Invalid selected_car index: {selected_car_index} (owned cars: {len(owned_cars)})")
            
            if selected_car_index >= len(garage_data):
                issues.append(f"Selected car index {selected_car_index} exceeds garage data length {len(garage_data)}")
            
            if selected_car_index < len(garage_data):
                garage_entry = garage_data[selected_car_index]
                if garage_entry.get("index") != selected_car_index:
                    issues.append(f"Garage entry index mismatch: expected {selected_car_index}, got {garage_entry.get('index')}")
            
            return len(issues) == 0, issues
            
        except Exception as e:
            return False, [f"Validation check failed: {e}"]

# Import time module for timestamps
import time
