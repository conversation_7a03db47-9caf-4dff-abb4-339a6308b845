import pygame
from background import Background
from ui_components import TextButton
from save_system import save_system
import json

class SaveSlotCard:
    def __init__(self, slot_data, x, y, width, height):
        self.slot_data = slot_data
        self.rect = pygame.Rect(x, y, width, height)
        self.is_hovered = False
        self.font = pygame.font.SysFont("arial", 24)
        self.header_font = pygame.font.SysFont("arial", 32)
        self.small_font = pygame.font.SysFont("arial", 18)
    
    def update(self, mouse_pos, mouse_click):
        self.is_hovered = self.rect.collidepoint(mouse_pos)
        if self.is_hovered and mouse_click[0]:
            return True
        return False
    
    def draw(self, screen):
        # Card background
        bg_color = (80, 80, 80) if self.slot_data["exists"] else (40, 40, 40)
        if self.is_hovered:
            bg_color = (100, 100, 100)
        
        pygame.draw.rect(screen, bg_color, self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)
        
        # Slot number
        slot_text = self.header_font.render(f"Slot {self.slot_data['slot']}", True, (255, 255, 255))
        screen.blit(slot_text, (self.rect.x + 10, self.rect.y + 10))
        
        if self.slot_data["exists"]:
            # Player info
            username_text = self.font.render(f"Gracz: {self.slot_data['username']}", True, (200, 200, 200))
            level_text = self.font.render(f"Poziom: {self.slot_data['level']}", True, (200, 200, 200))
            money_text = self.font.render(f"Pieniądze: {self.slot_data['money']} $", True, (200, 200, 200))
            date_text = self.small_font.render(f"Zapisano: {self.slot_data['save_date']}", True, (150, 150, 150))
            time_text = self.small_font.render(f"Czas gry: {self.slot_data['playtime']}", True, (150, 150, 150))
            
            screen.blit(username_text, (self.rect.x + 10, self.rect.y + 50))
            screen.blit(level_text, (self.rect.x + 10, self.rect.y + 80))
            screen.blit(money_text, (self.rect.x + 10, self.rect.y + 110))
            screen.blit(date_text, (self.rect.x + 10, self.rect.y + 140))
            screen.blit(time_text, (self.rect.x + 10, self.rect.y + 160))
        else:
            # Empty slot
            empty_text = self.font.render("Pusty slot", True, (150, 150, 150))
            screen.blit(empty_text, (self.rect.x + 10, self.rect.y + 80))

class ConfirmDialog:
    def __init__(self, message, s_width, s_height):
        self.width = 400
        self.height = 150
        self.rect = pygame.Rect(
            (s_width - self.width) // 2,
            (s_height - self.height) // 2,
            self.width,
            self.height
        )
        self.message = message
        self.font = pygame.font.SysFont("arial", 24)

        # Create buttons
        button_y = self.rect.y + self.height - 50
        self.yes_button = pygame.Rect(self.rect.x + 50, button_y, 100, 35)
        self.no_button = pygame.Rect(self.rect.x + self.width - 150, button_y, 100, 35)

        # Anti-misclick protection for critical save/load operations
        self.creation_time = pygame.time.get_ticks()
        self.min_interaction_delay = 400  # 400ms delay for save/load operations
        self.last_click_time = 0
        self.click_debounce_delay = 200  # 200ms between clicks
    
    def update(self, mouse_pos, mouse_click):
        current_time = pygame.time.get_ticks()

        # Prevent interactions too soon after dialog creation
        if current_time - self.creation_time < self.min_interaction_delay:
            return None

        # Debounce clicks
        if mouse_click[0] and current_time - self.last_click_time > self.click_debounce_delay:
            self.last_click_time = current_time
            if self.yes_button.collidepoint(mouse_pos):
                return "yes"
            elif self.no_button.collidepoint(mouse_pos):
                return "no"
        return None
    
    def draw(self, screen):
        # Dialog background
        pygame.draw.rect(screen, (40, 40, 40), self.rect)
        pygame.draw.rect(screen, (200, 200, 200), self.rect, 2)
        
        # Message
        message_text = self.font.render(self.message, True, (255, 255, 255))
        text_x = self.rect.x + (self.width - message_text.get_width()) // 2
        screen.blit(message_text, (text_x, self.rect.y + 30))
        
        # Buttons
        current_time = pygame.time.get_ticks()
        buttons_enabled = current_time - self.creation_time >= self.min_interaction_delay

        for button, text in [(self.yes_button, "Tak"), (self.no_button, "Nie")]:
            # Dim buttons if not yet enabled
            if buttons_enabled:
                bg_color = (60, 60, 60)
                text_color = (255, 255, 255)
            else:
                bg_color = (30, 30, 30)  # Darker version
                text_color = (150, 150, 150)

            pygame.draw.rect(screen, bg_color, button)
            pygame.draw.rect(screen, (200, 200, 200), button, 1)

            button_text = self.font.render(text, True, text_color)
            text_x = button.x + (button.width - button_text.get_width()) // 2
            text_y = button.y + (button.height - button_text.get_height()) // 2
            screen.blit(button_text, (text_x, text_y))

        # Show countdown if buttons are not yet enabled
        if not buttons_enabled:
            remaining_time = (self.min_interaction_delay - (current_time - self.creation_time)) / 1000.0
            countdown_text = pygame.font.SysFont("arial", 18).render(f"Poczekaj {remaining_time:.1f}s", True, (255, 200, 100))
            text_x = self.rect.x + (self.width - countdown_text.get_width()) // 2
            text_y = self.rect.y + self.height - 80
            screen.blit(countdown_text, (text_x, text_y))

def draw_save_menu(s_width, s_height, screen, mode="load"):
    """
    Draw save/load menu
    mode: "load" for loading saves, "save" for saving game
    """
    run = True
    bg = Background('background', s_width, s_height)
    
    # Create buttons
    back_button = TextButton('Powrót', 50, 50, font_size=36)
    new_game_button = None
    if mode == "load":
        new_game_button = TextButton('Nowa Gra', s_width - 200, 50, font_size=36)
    
    # Create fonts
    header_font = pygame.font.SysFont("arial", 48)
    info_font = pygame.font.SysFont("arial", 24)
    
    title = "Wczytaj Grę" if mode == "load" else "Zapisz Grę"
    header = header_font.render(title, True, (255, 255, 255))
    
    # Dialog state
    confirm_dialog = None
    selected_slot = None
    action_type = None
    
    while run:
        mouse_pos = pygame.mouse.get_pos()
        mouse_click = pygame.mouse.get_pressed()
        
        # Handle events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return "quit"
            if event.type == pygame.KEYDOWN and event.key == pygame.K_ESCAPE:
                if confirm_dialog:
                    confirm_dialog = None
                else:
                    return "back"
        
        # Update buttons
        back_button.update(mouse_pos, mouse_click)
        if new_game_button:
            new_game_button.update(mouse_pos, mouse_click)
        
        # Handle dialog
        if confirm_dialog:
            dialog_result = confirm_dialog.update(mouse_pos, mouse_click)
            if dialog_result == "yes":
                if action_type == "load":
                    if save_system.load_game(selected_slot):
                        return "loaded"
                elif action_type == "save":
                    if save_system.save_game(selected_slot):
                        return "saved"
                elif action_type == "delete":
                    save_system.delete_save(selected_slot)
                elif action_type == "new_game":
                    save_system.create_new_game()
                    return "new_game"
                confirm_dialog = None
            elif dialog_result == "no":
                confirm_dialog = None
        else:
            # Check back button
            if back_button.is_hovered and mouse_click[0]:
                return "back"
            
            # Check new game button
            if new_game_button and new_game_button.is_hovered and mouse_click[0]:
                confirm_dialog = ConfirmDialog("Rozpocząć nową grę?", s_width, s_height)
                action_type = "new_game"
        
        # Get save slots
        save_slots = save_system.get_save_slots()
        
        # Create save slot cards
        card_width = 350
        card_height = 200
        card_spacing = 50
        start_x = (s_width - (len(save_slots) * card_width + (len(save_slots) - 1) * card_spacing)) // 2
        start_y = 200
        
        slot_cards = []
        for i, slot_data in enumerate(save_slots):
            x = start_x + i * (card_width + card_spacing)
            card = SaveSlotCard(slot_data, x, start_y, card_width, card_height)
            slot_cards.append(card)
        
        # Handle slot card clicks
        if not confirm_dialog:
            for card in slot_cards:
                if card.update(mouse_pos, mouse_click):
                    slot_num = card.slot_data["slot"]
                    
                    if mode == "load":
                        if card.slot_data["exists"]:
                            confirm_dialog = ConfirmDialog(f"Wczytać grę ze slotu {slot_num}?", s_width, s_height)
                            selected_slot = slot_num
                            action_type = "load"
                    else:  # save mode
                        if card.slot_data["exists"]:
                            confirm_dialog = ConfirmDialog(f"Nadpisać zapis w slocie {slot_num}?", s_width, s_height)
                        else:
                            confirm_dialog = ConfirmDialog(f"Zapisać grę w slocie {slot_num}?", s_width, s_height)
                        selected_slot = slot_num
                        action_type = "save"
        
        # Handle right-click for delete (only in load mode)
        if mode == "load" and not confirm_dialog:
            mouse_right_click = pygame.mouse.get_pressed()[2]
            if mouse_right_click:
                for card in slot_cards:
                    if card.is_hovered and card.slot_data["exists"]:
                        confirm_dialog = ConfirmDialog(f"Usunąć zapis ze slotu {card.slot_data['slot']}?", s_width, s_height)
                        selected_slot = card.slot_data["slot"]
                        action_type = "delete"
        
        # Draw everything
        bg.draw(screen)
        
        # Draw header
        screen.blit(header, (s_width // 2 - header.get_width() // 2, 50))
        
        # Draw instructions
        if mode == "load":
            instruction = info_font.render("Kliknij na slot aby wczytać grę. PPM aby usunąć zapis.", True, (200, 200, 200))
        else:
            instruction = info_font.render("Kliknij na slot aby zapisać grę.", True, (200, 200, 200))
        screen.blit(instruction, (s_width // 2 - instruction.get_width() // 2, 120))
        
        # Draw slot cards
        for card in slot_cards:
            card.draw(screen)
        
        # Draw buttons
        back_button.draw(screen)
        if new_game_button:
            new_game_button.draw(screen)
        
        # Draw dialog if active
        if confirm_dialog:
            # Draw overlay
            overlay = pygame.Surface((s_width, s_height))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            screen.blit(overlay, (0, 0))
            confirm_dialog.draw(screen)
        
        pygame.display.update()
    
    return "back"
